#!/usr/bin/env python3

import click
import cv2
import numpy as np
from typing import Tuple, List
import os
from stabVid import StabVid
import tempfile
import glob
import signal
import sys

# Global state for cleanup
_cleanup_info = {'temp_dir': None, 'temp_input': None}

def compute_frame_similarity(frame1: np.ndarray, frame2: np.ndarray) -> float:
    """Compute similarity between two frames using MSE and structural similarity."""
    # Convert to grayscale for SSIM
    gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
    
    # Compute SSIM (structural similarity)
    ssim = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)[0][0]
    
    # Also compute MSE for additional measure
    diff = cv2.absdiff(frame1, frame2)
    mse = np.mean(diff)
    
    # Combine both metrics (SSIM ranges from -1 to 1, higher is better)
    # Convert to a distance metric where lower is better
    return (1 - ssim) * mse

def find_loop_candidates(similarities: np.ndarray, min_loop_length: int = 30, threshold_factor: float = 0.7) -> List[Tuple[int, int, float]]:
    """Find potential loop points based on frame similarities."""
    candidates = []
    
    # Use a more lenient threshold based on the distribution of similarities
    threshold = np.percentile(similarities, 25) * threshold_factor
    
    for i in range(len(similarities)):
        for j in range(i + min_loop_length, len(similarities)):
            if similarities[i][j] < threshold:
                # Check if the transition would be smooth
                if j < len(similarities) - 1:
                    transition_quality = similarities[i][j+1] + similarities[i-1][j]
                    if transition_quality < threshold * 2:
                        score = similarities[i][j] + (1.0 - (j - i) / len(similarities)) * 0.5
                        candidates.append((i, j, score))
    
    # Sort by score
    candidates.sort(key=lambda x: x[2])
    return candidates[:5]

def save_video(frames: List[np.ndarray], output_path: str, fps: float, width: int, height: int) -> bool:
    """Save a sequence of frames to a video file. Returns True if successful, False otherwise."""
    try:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        if not out.isOpened():
            click.echo(f"Error: Could not open video writer for {output_path}")
            return False

        for frame in frames:
            out.write(frame)
        out.release()

        # Verify the file was created and has content
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            return True
        else:
            click.echo(f"Error: Video file {output_path} was not created properly")
            return False

    except Exception as e:
        click.echo(f"Error saving video to {output_path}: {str(e)}")
        return False

def interpolate_frames_func(frame1: np.ndarray, frame2: np.ndarray, num_interpolated: int = 3) -> List[np.ndarray]:
    """Generate interpolated frames between two frames using optical flow-based morphing.

    Args:
        frame1: First frame (last frame of the loop)
        frame2: Second frame (first frame of the loop)
        num_interpolated: Number of frames to interpolate between frame1 and frame2

    Returns:
        List of interpolated frames
    """
    if num_interpolated <= 0:
        return []

    interpolated_frames = []

    try:
        # Create interpolated frames with enhanced blending
        for i in range(1, num_interpolated + 1):
            alpha = i / (num_interpolated + 1)

            # Linear interpolation with smoothing
            interpolated = cv2.addWeighted(frame1, 1 - alpha, frame2, alpha, 0)

            # Apply slight gaussian blur to smooth the interpolation
            interpolated = cv2.GaussianBlur(interpolated, (3, 3), 0.5)

            interpolated_frames.append(interpolated)

    except Exception:
        # Fallback to simple linear interpolation if optical flow fails
        for i in range(1, num_interpolated + 1):
            alpha = i / (num_interpolated + 1)
            interpolated = cv2.addWeighted(frame1, 1 - alpha, frame2, alpha, 0)
            interpolated_frames.append(interpolated)

    return interpolated_frames

def check_ffmpeg_available() -> bool:
    """Check if FFmpeg is available via imageio-ffmpeg or system PATH."""
    try:
        import imageio_ffmpeg
        # Try to get FFmpeg from imageio-ffmpeg
        imageio_ffmpeg.get_ffmpeg_exe()
        return True
    except (ImportError, Exception):
        # Fall back to system PATH
        import shutil
        return shutil.which('ffmpeg') is not None

def cleanup_temp_files(temp_dir=None, temp_input=None):
    """Clean up temporary files and artifacts from stabilization"""
    try:
        temp_dir = temp_dir or _cleanup_info['temp_dir']
        temp_input = temp_input or _cleanup_info['temp_input']

        if temp_input and os.path.exists(temp_input):
            os.unlink(temp_input)
        # Clean up any ffmpeg stabilization artifacts
        for artifact in ["transforms.trf", "zoomed.mp4"]:
            if os.path.exists(artifact):
                os.unlink(artifact)
        if temp_dir and os.path.exists(temp_dir):
            os.rmdir(temp_dir)
    except Exception as e:
        click.echo(f"Warning: Could not clean up some temporary files: {str(e)}")

def signal_handler(signum, frame):
    """Handle interrupt signal"""
    click.echo("\nInterrupted by user. Cleaning up...")
    cleanup_temp_files()
    sys.exit(1)

@click.command()
@click.option('-i', '--input', required=True, help='Input video file')
@click.option('-o', '--output', required=True, help='Output video file')
@click.option('--min-length', default=24, help='Minimum loop length in frames')
@click.option('--threshold', default=0.7, help='Threshold factor for similarity (0.1-1.0)')
@click.option('--candidate', default=0, help='Which loop candidate to use (0-4), if multiple are found')
@click.option('--interpolate-frames', default=3, help='Number of interpolated frames to add between last and first frame (0 to disable)')
def main(input, output, min_length, threshold, candidate, interpolate_frames):
    """Find perfect loops within a video file."""
    # Set up signal handler for cleanup on interrupt
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        cap = cv2.VideoCapture(input)
        if not cap.isOpened():
            raise click.ClickException("Could not open input video file")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        click.echo(f"Processing video: {input}")
        click.echo(f"FPS: {fps}, Total frames: {frame_count}")
        click.echo(f"Resolution: {width}x{height}")
        
        # Store frames and compute similarities
        frames = []
        with click.progressbar(length=frame_count, label='Reading frames') as bar:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
                bar.update(1)
        
        if len(frames) < min_length:
            raise click.ClickException("Video is too short for the specified minimum loop length")
        
        # Compute similarity matrix
        similarities = np.zeros((len(frames), len(frames)))
        with click.progressbar(length=len(frames), label='Analyzing frames') as bar:
            for i in range(len(frames)):
                for j in range(i + 1, len(frames)):
                    similarity = compute_frame_similarity(frames[i], frames[j])
                    similarities[i][j] = similarity
                    similarities[j][i] = similarity
                bar.update(1)
        
        # Find best loop candidates
        candidates = find_loop_candidates(similarities, min_length, threshold)
        if not candidates:
            raise click.ClickException("No suitable loop candidates found. Try adjusting --threshold (higher value = more lenient)")
        
        click.echo(f"\nFound {len(candidates)} potential loops:")
        for idx, (start, end, score) in enumerate(candidates):
            length = end - start
            click.echo(f"Candidate {idx}: frames {start} to {end} (length: {length} frames, {length/fps:.2f} seconds, score: {score:.3f})")
        
        if candidate >= len(candidates):
            candidate = 0
            click.echo(f"\nRequested candidate {candidate} not found, using best match (0)")
        
        # Use the selected candidate
        start_frame, end_frame, score = candidates[candidate]
        base_loop_length = end_frame - start_frame
        total_loop_length = base_loop_length + interpolate_frames
        click.echo(f"\nUsing loop candidate {candidate}: frames {start_frame} to {end_frame}")
        click.echo(f"Base loop length: {base_loop_length} frames ({base_loop_length/fps:.2f} seconds)")
        if interpolate_frames > 0:
            click.echo(f"Total loop length (with interpolation): {total_loop_length} frames ({total_loop_length/fps:.2f} seconds)")
        click.echo(f"Loop score: {score:.3f} (lower is better)")
        
        # Extract loop frames
        loop_frames = []

        # Add all frames from start to end (excluding the end frame to avoid duplication)
        for i in range(start_frame, end_frame):
            loop_frames.append(frames[i])

        # Add interpolated frames between last and first frame for smoother looping
        if interpolate_frames > 0:
            click.echo(f"Adding {interpolate_frames} interpolated frames between last and first frame...")
            last_frame = frames[end_frame - 1]  # Last frame of the loop
            first_frame = frames[start_frame]   # First frame of the loop

            interpolated = interpolate_frames_func(last_frame, first_frame, interpolate_frames)
            loop_frames.extend(interpolated)

            click.echo(f"✓ Added {len(interpolated)} interpolated frames")
        else:
            click.echo("Frame interpolation disabled")
        
        # Save original loop first
        name, ext = os.path.splitext(output)
        original_output = output

        # Save original version before asking about stabilization
        click.echo(f"Saving original loop to: {original_output}")
        if not save_video(loop_frames, original_output, fps, width, height):
            raise click.ClickException(f"Failed to save loop video to {original_output}")

        click.echo(f"✓ Successfully saved loop to: {original_output}")
        file_size = os.path.getsize(original_output)
        click.echo(f"  File size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
        
        # Ask about stabilization, defaulting to Yes
        if click.confirm("Do you want to stabilize the loop?", default=True):
            # Check if FFmpeg is available
            if not check_ffmpeg_available():
                click.echo("❌ FFmpeg not found!")
                click.echo("\nTo enable video stabilization, please install imageio-ffmpeg:")
                click.echo("  pip install imageio-ffmpeg")
                click.echo("\nThis will automatically download FFmpeg binaries for your system.")
                click.echo("\nSkipping stabilization. Your original loop is saved and ready to use!")
            else:
                click.echo("Stabilizing video...")

                # Create temporary directory for stabilization
                temp_dir = tempfile.mkdtemp()
                temp_input = os.path.join(temp_dir, "temp_input.mp4")
                stabilized_output = f"{name}_stabilized{ext}"

                # Store cleanup info globally
                _cleanup_info['temp_dir'] = temp_dir
                _cleanup_info['temp_input'] = temp_input

                try:
                    # Copy original loop to temporary location
                    if not save_video(loop_frames, temp_input, fps, width, height):
                        raise Exception("Failed to create temporary video file for stabilization")

                    # Use stabVid for stabilization
                    stabilizer = StabVid(max_video_length_seconds=600, min_video_length_seconds=0.1)
                    stabilizer(temp_input, stabilized_output)
                    click.echo("✓ Stabilization complete")
                    click.echo(f"✓ Stabilized loop saved to: {stabilized_output}")
                except Exception as e:
                    click.echo(f"❌ Error during stabilization: {str(e)}")
                    click.echo("✓ Original loop was saved and is still available")
                    if os.path.exists(stabilized_output):
                        os.unlink(stabilized_output)
                finally:
                    cleanup_temp_files()
                    # Reset cleanup info
                    _cleanup_info['temp_dir'] = None
                    _cleanup_info['temp_input'] = None
            
        cap.release()
        click.echo("Processing complete")
        
    except Exception as e:
        cleanup_temp_files()
        raise click.ClickException(str(e))

if __name__ == '__main__':
    main()